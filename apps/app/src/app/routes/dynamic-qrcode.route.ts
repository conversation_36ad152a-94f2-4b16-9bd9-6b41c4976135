import { Request, Response, NextFunction } from 'express';
import { AngularNodeAppEngine, writeResponseToNodeResponse } from '@angular/ssr/node';

export async function handleDynamicQRCode(req: Request, res: Response, next: NextFunction, angular: AngularNodeAppEngine): Promise<void> {
  console.log(req.headers);
  if (req.method === 'HEAD') {
    console.log('HEAD HEAD!!!!')
    console.log(req.headers);
  } else if (req.method === 'GET') {
    angular
      .handle(req, {
        'foo': 'Awesome QR Code',
      })
      .then((response) =>
        response ? writeResponseToNodeResponse(response, res) : next(),
      )
      .catch(next);
    return;
  }

  next();
}
